import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, Between } from 'typeorm';

import { Inspector } from './entities/inspector.entity';
import { Schedule } from '../schedules/entities/schedule.entity';
import { CreateInspectorDto } from './dto/create-inspector.dto';
import { UpdateInspectorDto } from './dto/update-inspector.dto';
import { InspectorQueryDto } from './dto/inspector-query.dto';

@Injectable()
export class InspectorsService {
  constructor(
    @InjectRepository(Inspector)
    private readonly inspectorRepository: Repository<Inspector>,
    @InjectRepository(Schedule)
    private readonly scheduleRepository: Repository<Schedule>,
  ) {}

  async create(createInspectorDto: CreateInspectorDto) {
    // Check if inspector with email already exists
    const existingInspector = await this.inspectorRepository.findOne({
      where: { email: createInspectorDto.email },
    });

    if (existingInspector) {
      throw new ConflictException('Inspector with this email already exists');
    }

    const inspector = this.inspectorRepository.create(createInspectorDto);
    const savedInspector = await this.inspectorRepository.save(inspector);

    return {
      inspector: savedInspector,
      message: 'Inspector created successfully',
    };
  }

  async findAll(query: InspectorQueryDto) {
    const {
      page = 1,
      limit = 10,
      isActive,
      isAvailable,
      search,
      specialization,
      sortBy = 'name',
      sortOrder = 'ASC',
    } = query;

    const queryBuilder = this.inspectorRepository.createQueryBuilder('inspector');

    // Apply filters
    if (isActive !== undefined) {
      queryBuilder.andWhere('inspector.isActive = :isActive', { isActive });
    }

    if (isAvailable !== undefined) {
      queryBuilder.andWhere('inspector.isAvailable = :isAvailable', { isAvailable });
    }

    if (search) {
      queryBuilder.andWhere(
        '(inspector.name ILIKE :search OR inspector.email ILIKE :search OR inspector.licenseNumber ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (specialization) {
      queryBuilder.andWhere(':specialization = ANY(inspector.specializations)', {
        specialization,
      });
    }

    // Apply sorting
    queryBuilder.orderBy(`inspector.${sortBy}`, sortOrder);

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [inspectors, total] = await queryBuilder.getManyAndCount();

    return {
      inspectors,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const inspector = await this.inspectorRepository.findOne({
      where: { id },
      relations: ['schedules'],
    });

    if (!inspector) {
      throw new NotFoundException('Inspector not found');
    }

    return inspector;
  }

  async update(id: number, updateInspectorDto: UpdateInspectorDto) {
    const inspector = await this.inspectorRepository.findOne({ where: { id } });

    if (!inspector) {
      throw new NotFoundException('Inspector not found');
    }

    // Check if email is being changed and if it's already taken
    if (updateInspectorDto.email && updateInspectorDto.email !== inspector.email) {
      const existingInspector = await this.inspectorRepository.findOne({
        where: { email: updateInspectorDto.email },
      });

      if (existingInspector) {
        throw new ConflictException('Email already in use');
      }
    }

    await this.inspectorRepository.update(id, updateInspectorDto);

    const updatedInspector = await this.findOne(id);
    return {
      inspector: updatedInspector,
      message: 'Inspector updated successfully',
    };
  }

  async remove(id: number) {
    const inspector = await this.inspectorRepository.findOne({ where: { id } });

    if (!inspector) {
      throw new NotFoundException('Inspector not found');
    }

    // Check if inspector has active schedules
    const activeSchedules = await this.scheduleRepository.count({
      where: { inspectorId: id, available: false },
    });

    if (activeSchedules > 0) {
      throw new ConflictException('Cannot delete inspector with active schedules');
    }

    await this.inspectorRepository.remove(inspector);

    return {
      message: 'Inspector deleted successfully',
    };
  }

  async findAvailable(date: string, startTime: string, endTime: string) {
    // Find inspectors who are active and available
    const availableInspectors = await this.inspectorRepository.find({
      where: { isActive: true, isAvailable: true },
    });

    // Filter out inspectors who have conflicting schedules
    const inspectorsWithoutConflicts = [];

    for (const inspector of availableInspectors) {
      const conflicts = await this.scheduleRepository.count({
        where: {
          inspectorId: inspector.id,
          date,
          startTime: Between(startTime, endTime),
        },
      });

      if (conflicts === 0) {
        // Check daily assignment limit (max 3 per day)
        const dailyAssignments = await this.scheduleRepository.count({
          where: {
            inspectorId: inspector.id,
            date,
            available: false,
          },
        });

        if (dailyAssignments < 3) {
          inspectorsWithoutConflicts.push({
            ...inspector,
            dailyAssignments,
            availableSlots: 3 - dailyAssignments,
          });
        }
      }
    }

    return {
      date,
      timeSlot: { startTime, endTime },
      availableInspectors: inspectorsWithoutConflicts,
    };
  }

  async getSchedule(id: number, startDate?: string, endDate?: string) {
    const inspector = await this.findOne(id);

    const queryBuilder = this.scheduleRepository.createQueryBuilder('schedule');
    queryBuilder.where('schedule.inspectorId = :id', { id });

    if (startDate && endDate) {
      queryBuilder.andWhere('schedule.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    queryBuilder.leftJoinAndSelect('schedule.order', 'order');
    queryBuilder.orderBy('schedule.date', 'ASC');
    queryBuilder.addOrderBy('schedule.startTime', 'ASC');

    const schedules = await queryBuilder.getMany();

    return {
      inspector: {
        id: inspector.id,
        name: inspector.name,
        email: inspector.email,
      },
      schedules,
      period: { startDate, endDate },
    };
  }

  async getStats(id: number) {
    const inspector = await this.findOne(id);

    const totalSchedules = await this.scheduleRepository.count({
      where: { inspectorId: id },
    });

    const completedInspections = await this.scheduleRepository.count({
      where: { inspectorId: id, available: false },
    });

    const upcomingSchedules = await this.scheduleRepository.count({
      where: {
        inspectorId: id,
        date: Between(new Date().toISOString().split('T')[0], '2099-12-31'),
      },
    });

    // Calculate average rating from completed inspections
    const avgRating = inspector.rating || 0;

    // Get monthly statistics
    const monthlyStats = await this.getMonthlyStats(id);

    return {
      inspector: {
        id: inspector.id,
        name: inspector.name,
        rating: avgRating,
        completedInspections: inspector.completedInspections,
      },
      statistics: {
        totalSchedules,
        completedInspections,
        upcomingSchedules,
        averageRating: avgRating,
        monthlyStats,
      },
    };
  }

  async updateAvailability(id: number, isAvailable: boolean) {
    const inspector = await this.inspectorRepository.findOne({ where: { id } });

    if (!inspector) {
      throw new NotFoundException('Inspector not found');
    }

    await this.inspectorRepository.update(id, { isAvailable });

    return {
      message: `Inspector availability updated to ${isAvailable ? 'available' : 'unavailable'}`,
    };
  }

  async updateRating(id: number, rating: number) {
    const inspector = await this.inspectorRepository.findOne({ where: { id } });

    if (!inspector) {
      throw new NotFoundException('Inspector not found');
    }

    // Calculate new average rating
    const totalRatings = inspector.completedInspections || 1;
    const currentTotal = (inspector.rating || 0) * totalRatings;
    const newAverage = (currentTotal + rating) / (totalRatings + 1);

    await this.inspectorRepository.update(id, {
      rating: Math.round(newAverage * 100) / 100, // Round to 2 decimal places
    });

    return {
      message: 'Inspector rating updated successfully',
      newRating: newAverage,
    };
  }

  async getSpecializations() {
    const inspectors = await this.inspectorRepository.find({
      select: ['specializations'],
      where: { isActive: true },
    });

    const allSpecializations = inspectors
      .flatMap(inspector => inspector.specializations || [])
      .filter((spec, index, array) => array.indexOf(spec) === index)
      .sort();

    return allSpecializations;
  }

  private async getMonthlyStats(inspectorId: number) {
    // Get statistics for the last 12 months
    const monthlyData = [];
    const currentDate = new Date();

    for (let i = 11; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const startDate = date.toISOString().split('T')[0];
      const endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0)
        .toISOString()
        .split('T')[0];

      const monthlySchedules = await this.scheduleRepository.count({
        where: {
          inspectorId,
          date: Between(startDate, endDate),
        },
      });

      const monthlyCompletions = await this.scheduleRepository.count({
        where: {
          inspectorId,
          date: Between(startDate, endDate),
          available: false,
        },
      });

      monthlyData.push({
        month: date.toISOString().substring(0, 7), // YYYY-MM format
        schedules: monthlySchedules,
        completions: monthlyCompletions,
      });
    }

    return monthlyData;
  }
}
