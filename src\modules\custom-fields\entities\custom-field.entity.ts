import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { CustomFieldValue } from './custom-field-value.entity';

export enum CustomFieldType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  NUMBER = 'number',
  EMAIL = 'email',
  PHONE = 'phone',
  URL = 'url',
  DATE = 'date',
  DATETIME = 'datetime',
  BOOLEAN = 'boolean',
  SELECT = 'select',
  MULTISELECT = 'multiselect',
  RADIO = 'radio',
  CHECKBOX = 'checkbox',
  FILE = 'file',
  IMAGE = 'image',
  JSON = 'json',
}

export enum CustomFieldEntity {
  ORDER = 'order',
  PROPERTY = 'property',
  INSPECTOR = 'inspector',
  USER = 'user',
  SCHEDULE = 'schedule',
}

@Entity('custom_fields')
export class CustomField {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100 })
  name: string;

  @Column({ unique: true, length: 100 })
  key: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: CustomFieldType,
  })
  type: CustomFieldType;

  @Column({
    type: 'enum',
    enum: CustomFieldEntity,
  })
  entityType: CustomFieldEntity;

  @Column({ type: 'jsonb', nullable: true })
  options: {
    choices?: { value: string; label: string }[];
    placeholder?: string;
    helpText?: string;
    validation?: {
      required?: boolean;
      minLength?: number;
      maxLength?: number;
      min?: number;
      max?: number;
      pattern?: string;
    };
    display?: {
      width?: string;
      columns?: number;
      rows?: number;
    };
  };

  @Column({ type: 'text', nullable: true })
  defaultValue: string;

  @Column({ default: false })
  isRequired: boolean;

  @Column({ default: true })
  isActive: boolean;

  @Column({ default: true })
  isVisible: boolean;

  @Column({ default: false })
  isSearchable: boolean;

  @Column({ default: 0 })
  sortOrder: number;

  @Column({ nullable: true, length: 100 })
  group: string;

  @Column({ type: 'text', array: true, nullable: true })
  permissions: string[]; // Which roles can view/edit this field

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToMany(() => CustomFieldValue, (value) => value.customField)
  values: CustomFieldValue[];
}
