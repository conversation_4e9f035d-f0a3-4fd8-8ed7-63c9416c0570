import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';

import { SchedulesService } from './schedules.service';
import { CreateScheduleDto } from './dto/create-schedule.dto';
import { UpdateScheduleDto } from './dto/update-schedule.dto';
import { ScheduleQueryDto } from './dto/schedule-query.dto';
import { CheckConflictDto } from './dto/check-conflict.dto';
import { BulkCreateScheduleDto } from './dto/bulk-create-schedule.dto';
import { Auth } from '../../common/decorators/auth.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';

@ApiTags('Schedules')
@Controller('schedules')
export class SchedulesController {
  constructor(private readonly schedulesService: SchedulesService) {}

  // @Post()
  // @Auth('admin', 'inspector')
  // @ApiOperation({ summary: 'Create a new schedule' })
  // @ApiResponse({ status: 201, description: 'Schedule successfully created' })
  // @ApiResponse({ status: 400, description: 'Bad request' })
  // @ApiResponse({ status: 409, description: 'Schedule conflict' })
  // async create(
  //   @Body() createScheduleDto: CreateScheduleDto,
  //   @CurrentUser() user: any,
  // ) {
  //   return this.schedulesService.create(createScheduleDto, user);
  // }

  // @Post('bulk')
  // @Auth('admin', 'inspector')
  // @ApiOperation({ summary: 'Create multiple schedules' })
  // @ApiResponse({ status: 201, description: 'Schedules successfully created' })
  // @ApiResponse({ status: 400, description: 'Bad request' })
  // async bulkCreate(
  //   @Body() bulkCreateDto: BulkCreateScheduleDto,
  //   @CurrentUser() user: any,
  // ) {
  //   return this.schedulesService.bulkCreate(bulkCreateDto, user);
  // }

  // @Get()
  // @Auth('admin', 'inspector', 'client')
  // @ApiOperation({ summary: 'Get all schedules with filtering' })
  // @ApiResponse({ status: 200, description: 'Schedules retrieved successfully' })
  // @ApiQuery({ name: 'page', required: false, type: Number })
  // @ApiQuery({ name: 'limit', required: false, type: Number })
  // @ApiQuery({ name: 'inspectorId', required: false, type: Number })
  // @ApiQuery({ name: 'date', required: false, type: String })
  // @ApiQuery({ name: 'available', required: false, type: Boolean })
  // async findAll(
  //   @Query() query: ScheduleQueryDto,
  //   @CurrentUser() user: any,
  // ) {
  //   return this.schedulesService.findAll(query, user);
  // }

  // @Get('conflicts')
  // @Auth('admin', 'inspector')
  // @ApiOperation({ summary: 'Check for schedule conflicts' })
  // @ApiResponse({ status: 200, description: 'Conflict check completed' })
  // async checkConflicts(@Query() checkConflictDto: CheckConflictDto) {
  //   return this.schedulesService.checkConflicts(checkConflictDto);
  // }

  // @Get('availability/:inspectorId')
  // @Auth('admin', 'inspector', 'client')
  // @ApiOperation({ summary: 'Get inspector availability' })
  // @ApiResponse({ status: 200, description: 'Availability retrieved successfully' })
  // @ApiQuery({ name: 'startDate', required: true, type: String })
  // @ApiQuery({ name: 'endDate', required: true, type: String })
  // async getAvailability(
  //   @Param('inspectorId', ParseIntPipe) inspectorId: number,
  //   @Query('startDate') startDate: string,
  //   @Query('endDate') endDate: string,
  // ) {
  //   return this.schedulesService.getInspectorAvailability(inspectorId, startDate, endDate);
  // }

  // @Get('calendar/:inspectorId')
  // @Auth('admin', 'inspector')
  // @ApiOperation({ summary: 'Get inspector calendar view' })
  // @ApiResponse({ status: 200, description: 'Calendar data retrieved successfully' })
  // @ApiQuery({ name: 'month', required: false, type: String })
  // @ApiQuery({ name: 'year', required: false, type: String })
  // async getCalendar(
  //   @Param('inspectorId', ParseIntPipe) inspectorId: number,
  //   @Query('month') month?: string,
  //   @Query('year') year?: string,
  // ) {
  //   return this.schedulesService.getCalendarView(inspectorId, month, year);
  // }

  // @Get(':id')
  // @Auth('admin', 'inspector', 'client')
  // @ApiOperation({ summary: 'Get schedule by ID' })
  // @ApiResponse({ status: 200, description: 'Schedule retrieved successfully' })
  // @ApiResponse({ status: 404, description: 'Schedule not found' })
  // async findOne(
  //   @Param('id', ParseIntPipe) id: number,
  //   @CurrentUser() user: any,
  // ) {
  //   return this.schedulesService.findOne(id, user);
  // }

  // @Patch(':id')
  // @Auth('admin', 'inspector')
  // @ApiOperation({ summary: 'Update schedule' })
  // @ApiResponse({ status: 200, description: 'Schedule updated successfully' })
  // @ApiResponse({ status: 404, description: 'Schedule not found' })
  // @ApiResponse({ status: 409, description: 'Schedule conflict' })
  // async update(
  //   @Param('id', ParseIntPipe) id: number,
  //   @Body() updateScheduleDto: UpdateScheduleDto,
  //   @CurrentUser() user: any,
  // ) {
  //   return this.schedulesService.update(id, updateScheduleDto, user);
  // }

  // @Patch(':id/assign-order')
  // @Auth('admin', 'inspector')
  // @ApiOperation({ summary: 'Assign order to schedule' })
  // @ApiResponse({ status: 200, description: 'Order assigned successfully' })
  // async assignOrder(
  //   @Param('id', ParseIntPipe) id: number,
  //   @Body() assignData: { orderId: number },
  //   @CurrentUser() user: any,
  // ) {
  //   return this.schedulesService.assignOrder(id, assignData.orderId, user);
  // }

  // @Patch(':id/unassign-order')
  // @Auth('admin', 'inspector')
  // @ApiOperation({ summary: 'Unassign order from schedule' })
  // @ApiResponse({ status: 200, description: 'Order unassigned successfully' })
  // async unassignOrder(
  //   @Param('id', ParseIntPipe) id: number,
  //   @CurrentUser() user: any,
  // ) {
  //   return this.schedulesService.unassignOrder(id, user);
  // }

  // @Delete(':id')
  // @Auth('admin', 'inspector')
  // @ApiOperation({ summary: 'Delete schedule' })
  // @ApiResponse({ status: 200, description: 'Schedule deleted successfully' })
  // @ApiResponse({ status: 404, description: 'Schedule not found' })
  // async remove(
  //   @Param('id', ParseIntPipe) id: number,
  //   @CurrentUser() user: any,
  // ) {
  //   return this.schedulesService.remove(id, user);
  // }

  // @Get('stats/overview')
  // @Auth('admin')
  // @ApiOperation({ summary: 'Get schedule statistics' })
  // @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  // async getStats() {
  //   return this.schedulesService.getScheduleStats();
  // }

  // @Post('recurring')
  // @Auth('admin', 'inspector')
  // @ApiOperation({ summary: 'Create recurring schedules' })
  // @ApiResponse({ status: 201, description: 'Recurring schedules created' })
  // async createRecurring(
  //   @Body() recurringData: any,
  //   @CurrentUser() user: any,
  // ) {
  //   return this.schedulesService.createRecurringSchedules(recurringData, user);
  // }
}
