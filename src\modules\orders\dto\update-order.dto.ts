import { PartialType } from '@nestjs/swagger';
import { CreateOrderDto } from './create-order.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsArray, IsNumber } from 'class-validator';
import { OrderStatus } from '../entities/order.entity';

export class UpdateOrderDto extends PartialType(CreateOrderDto) {
  @ApiProperty({ 
    description: 'Order status', 
    enum: OrderStatus, 
    required: false 
  })
  @IsEnum(OrderStatus)
  @IsOptional()
  status?: OrderStatus;

  @ApiProperty({ 
    description: 'Assigned inspector IDs', 
    type: [Number], 
    required: false 
  })
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  assignedInspectorIds?: number[];

  @ApiProperty({ 
    description: 'Inspection date', 
    type: Date, 
    required: false 
  })
  @IsOptional()
  inspectionDate?: Date;
}
