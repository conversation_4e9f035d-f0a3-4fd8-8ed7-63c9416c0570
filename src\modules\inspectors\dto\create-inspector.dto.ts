import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsOptional,
  IsBoolean,
  IsNumber,
  IsArray,
  IsObject,
  IsNotEmpty,
  <PERSON>,
  <PERSON>,
} from 'class-validator';

export class CreateInspectorDto {
  @ApiProperty({
    description: 'Inspector full name',
    example: '<PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Inspector email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Inspector phone number',
    example: '+**********',
  })
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiProperty({
    description: 'Inspector license number',
    example: 'INS-12345',
  })
  @IsString()
  @IsNotEmpty()
  licenseNumber: string;

  @ApiProperty({
    description: 'License expiration date',
    example: '2025-12-31',
    required: false,
  })
  @IsString()
  @IsOptional()
  licenseExpiry?: string;

  @ApiProperty({
    description: 'Inspector specializations',
    type: [String],
    example: ['Residential', 'Commercial', 'Electrical'],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  specializations?: string[];

  @ApiProperty({
    description: 'Years of experience',
    example: 5,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  @Min(0)
  experience?: number;

  @ApiProperty({
    description: 'Inspector rating (1-5)',
    example: 4.5,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  @Min(1)
  @Max(5)
  rating?: number;

  @ApiProperty({
    description: 'Number of completed inspections',
    example: 150,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  @Min(0)
  completedInspections?: number;

  @ApiProperty({
    description: 'Inspector bio/description',
    example: 'Experienced residential inspector with expertise in electrical systems',
    required: false,
  })
  @IsString()
  @IsOptional()
  bio?: string;

  @ApiProperty({
    description: 'Inspector address',
    example: '123 Main St, City, State 12345',
    required: false,
  })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiProperty({
    description: 'Emergency contact number',
    example: '+1987654321',
    required: false,
  })
  @IsString()
  @IsOptional()
  emergencyContact?: string;

  @ApiProperty({
    description: 'Is inspector active',
    default: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean = true;

  @ApiProperty({
    description: 'Is inspector available for assignments',
    default: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isAvailable?: boolean = true;

  @ApiProperty({
    description: 'Inspector certifications',
    type: [String],
    example: ['ASHI Certified', 'NAHI Member'],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  certifications?: string[];

  @ApiProperty({
    description: 'Inspector preferences and settings',
    type: Object,
    example: {
      maxDailyInspections: 3,
      preferredAreas: ['Downtown', 'Suburbs'],
      workingHours: { start: '08:00', end: '18:00' },
    },
    required: false,
  })
  @IsObject()
  @IsOptional()
  preferences?: Record<string, any>;
}
