import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString, IsNotEmpty } from 'class-validator';

export class SetCustomFieldValueDto {
  @ApiProperty({
    description: 'Custom field ID',
    example: 1,
  })
  @IsNumber()
  customFieldId: number;

  @ApiProperty({
    description: 'Entity type',
    example: 'order',
  })
  @IsString()
  @IsNotEmpty()
  entityType: string;

  @ApiProperty({
    description: 'Entity ID',
    example: 123,
  })
  @IsNumber()
  entityId: number;

  @ApiProperty({
    description: 'Field value',
    example: 'Sample value',
  })
  @IsNotEmpty()
  value: any;
}
