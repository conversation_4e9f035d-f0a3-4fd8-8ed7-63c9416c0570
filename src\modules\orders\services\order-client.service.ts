import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { User, UserRole } from '../../users/entities/user.entity';
import { Order } from '../entities/order.entity';
import { OrderClient } from '../entities/order-client.entity';

@Injectable()
export class OrderClientService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(OrderClient)
    private readonly orderClientRepository: Repository<OrderClient>,
  ) {}

  /**
   * Get client information for an order
   */
  async getOrderClients(orderId: number): Promise<User[]> {
    const orderClients = await this.orderClientRepository.find({
      where: { orderId },
      relations: ['client'],
      order: { isPrimary: 'DESC', createdAt: 'ASC' },
    });

    return orderClients.map(oc => oc.client);
  }



  /**
   * Get client emails for an order
   */
  async getOrderClientEmails(orderId: number): Promise<string[]> {
    const clients = await this.getOrderClients(orderId);
    return clients.map(client => client.email).filter(email => email);
  }

  /**
   * Get client names for an order
   */
  async getOrderClientNames(orderId: number): Promise<string[]> {
    const clients = await this.getOrderClients(orderId);
    return clients.map(client => client.name).filter(name => name);
  }

  /**
   * Get formatted client information for display
   */
  async getOrderClientInfo(orderId: number): Promise<{
    primaryClient: User | null;
    allClients: User[];
    clientNames: string[];
    clientEmails: string[];
    displayName: string;
    displayEmail: string;
  }> {
    const clients = await this.getOrderClients(orderId);
    const primaryClient = clients.length > 0 ? clients[0] : null;
    const clientNames = clients.map(client => client.name).filter(name => name);
    const clientEmails = clients.map(client => client.email).filter(email => email);

    // Create display strings
    const displayName = clientNames.length > 0 
      ? clientNames.length === 1 
        ? clientNames[0]
        : `${clientNames[0]} + ${clientNames.length - 1} others`
      : 'No clients assigned';

    const displayEmail = clientEmails.length > 0 ? clientEmails[0] : '';

    return {
      primaryClient,
      allClients: clients,
      clientNames,
      clientEmails,
      displayName,
      displayEmail,
    };
  }

  /**
   * Get orders for a specific client
   */
  async getClientOrders(clientId: number): Promise<Order[]> {
    const orderClients = await this.orderClientRepository.find({
      where: { clientId },
      relations: ['order'],
    });

    return orderClients.map(oc => oc.order);
  }

  /**
   * Check if a client is associated with an order
   */
  async isClientInOrder(orderId: number, clientId: number): Promise<boolean> {
    const orderClient = await this.orderClientRepository.findOne({
      where: { orderId, clientId },
    });

    return !!orderClient;
  }

  /**
   * Get client contact information for notifications
   */
  async getClientContactInfo(clientId: number): Promise<{
    name: string;
    email: string;
    phone: string;
  } | null> {
    const client = await this.userRepository.findOne({
      where: { id: clientId, role: UserRole.CLIENT },
    });

    if (!client) {
      return null;
    }

    return {
      name: client.name || '',
      email: client.email || '',
      phone: client.phone || '',
    };
  }

  /**
   * Get all contact information for order clients
   */
  async getOrderContactInfo(orderId: number): Promise<Array<{
    id: number;
    name: string;
    email: string;
    phone: string;
  }>> {
    const clients = await this.getOrderClients(orderId);
    
    return clients.map(client => ({
      id: client.id,
      name: client.name || '',
      email: client.email || '',
      phone: client.phone || '',
    }));
  }

  /**
   * Format client information for email templates
   */
  async getEmailTemplateVariables(orderId: number): Promise<{
    clientName: string;
    clientEmail: string;
    clientPhone: string;
    allClientNames: string[];
    allClientEmails: string[];
  }> {
    const clientInfo = await this.getOrderClientInfo(orderId);
    const primaryClient = clientInfo.primaryClient;

    return {
      clientName: clientInfo.displayName,
      clientEmail: clientInfo.displayEmail,
      clientPhone: primaryClient?.phone || '',
      allClientNames: clientInfo.clientNames,
      allClientEmails: clientInfo.clientEmails,
    };
  }

  /**
   * Validate that all client IDs exist and are valid clients
   */
  async validateClientIds(clientIds: number[]): Promise<{
    valid: boolean;
    validClients: User[];
    invalidIds: number[];
  }> {
    if (!clientIds || clientIds.length === 0) {
      return {
        valid: true,
        validClients: [],
        invalidIds: [],
      };
    }

    const clients = await this.userRepository.find({
      where: { id: In(clientIds), role: UserRole.CLIENT },
    });

    const validIds = clients.map(client => client.id);
    const invalidIds = clientIds.filter(id => !validIds.includes(id));

    return {
      valid: invalidIds.length === 0,
      validClients: clients,
      invalidIds,
    };
  }

  /**
   * Add client to order
   */
  async addClientToOrder(
    orderId: number,
    clientId: number,
    isPrimary: boolean = false,
    notes?: string,
  ): Promise<OrderClient> {
    // Check if order exists
    const order = await this.orderRepository.findOne({ where: { id: orderId } });
    if (!order) {
      throw new NotFoundException('Order not found');
    }

    // Check if client exists and is valid
    const client = await this.userRepository.findOne({
      where: { id: clientId, role: UserRole.CLIENT },
    });
    if (!client) {
      throw new NotFoundException('Client not found');
    }

    // Check if client is already assigned to this order
    const existingAssignment = await this.orderClientRepository.findOne({
      where: { orderId, clientId },
    });

    if (existingAssignment) {
      throw new BadRequestException('Client is already assigned to this order');
    }

    // If this is primary client, unset other primary clients
    if (isPrimary) {
      await this.orderClientRepository.update(
        { orderId, isPrimary: true },
        { isPrimary: false },
      );
    }

    // Create new assignment
    const orderClient = this.orderClientRepository.create({
      orderId,
      clientId,
      isPrimary,
      notes,
    });

    return await this.orderClientRepository.save(orderClient);
  }

  /**
   * Remove client from order
   */
  async removeClientFromOrder(orderId: number, clientId: number): Promise<void> {
    const orderClient = await this.orderClientRepository.findOne({
      where: { orderId, clientId },
    });

    if (!orderClient) {
      throw new NotFoundException('Client assignment not found');
    }

    await this.orderClientRepository.remove(orderClient);
  }

  /**
   * Replace all clients for an order
   */
  async replaceOrderClients(
    orderId: number,
    clientIds: number[],
    primaryClientId?: number,
  ): Promise<OrderClient[]> {
    // Validate all clients exist
    if (clientIds.length > 0) {
      const clients = await this.userRepository.find({
        where: { id: In(clientIds), role: UserRole.CLIENT },
      });
      if (clients.length !== clientIds.length) {
        throw new NotFoundException('One or more clients not found');
      }
    }

    // Remove all existing assignments
    await this.orderClientRepository.delete({ orderId });

    // Add new assignments
    const newAssignments: OrderClient[] = [];
    for (const clientId of clientIds) {
      const isPrimary = clientId === primaryClientId;
      const assignment = await this.addClientToOrder(orderId, clientId, isPrimary);
      newAssignments.push(assignment);
    }

    return newAssignments;
  }

  /**
   * Get client IDs for an order (for backward compatibility)
   */
  async getOrderClientIds(orderId: number): Promise<number[]> {
    const orderClients = await this.orderClientRepository.find({
      where: { orderId },
      order: { isPrimary: 'DESC', createdAt: 'ASC' },
    });

    return orderClients.map(oc => oc.clientId);
  }

  /**
   * Get primary client for an order
   */
  async getPrimaryClient(orderId: number): Promise<User | null> {
    const orderClient = await this.orderClientRepository.findOne({
      where: { orderId, isPrimary: true },
      relations: ['client'],
    });

    return orderClient?.client || null;
  }

  /**
   * Get client statistics for reporting
   */
  async getClientOrderStats(clientId: number): Promise<{
    totalOrders: number;
    completedOrders: number;
    pendingOrders: number;
    cancelledOrders: number;
  }> {
    const queryBuilder = this.orderClientRepository
      .createQueryBuilder('orderClient')
      .leftJoin('orderClient.order', 'order')
      .where('orderClient.clientId = :clientId', { clientId });

    const totalOrders = await queryBuilder.getCount();

    const completedOrders = await queryBuilder
      .clone()
      .andWhere('order.status = :status', { status: 'completed' })
      .getCount();

    const pendingOrders = await queryBuilder
      .clone()
      .andWhere('order.status IN (:...statuses)', {
        statuses: ['pending', 'assigned', 'scheduled', 'in_progress']
      })
      .getCount();

    const cancelledOrders = await queryBuilder
      .clone()
      .andWhere('order.status = :status', { status: 'cancelled' })
      .getCount();

    return {
      totalOrders,
      completedOrders,
      pendingOrders,
      cancelledOrders,
    };
  }
}
