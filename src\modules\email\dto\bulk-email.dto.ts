import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsArray,
  IsObject,
  IsEnum,
  IsNotEmpty,
  IsEmail,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class EmailRecipientDto {
  @ApiProperty({
    description: 'Recipient email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'Recipient-specific variables',
    example: { name: '<PERSON>', orderNumber: 'ORD-001' },
    required: false,
  })
  @IsObject()
  @IsOptional()
  variables?: Record<string, any>;
}

export class BulkEmailDto {
  @ApiProperty({
    description: 'List of email recipients',
    type: [EmailRecipientDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EmailRecipientDto)
  recipients: EmailRecipientDto[];

  @ApiProperty({
    description: 'Email subject template',
    example: 'Order Confirmation - {{orderNumber}}',
  })
  @IsString()
  @IsNotEmpty()
  subject: string;

  @ApiProperty({
    description: 'Plain text email template',
    example: 'Dear {{name}}, your order {{orderNumber}} has been confirmed.',
    required: false,
  })
  @IsString()
  @IsOptional()
  text?: string;

  @ApiProperty({
    description: 'HTML email template',
    example: '<h1>Dear {{name}}</h1><p>Your order {{orderNumber}} has been confirmed.</p>',
    required: false,
  })
  @IsString()
  @IsOptional()
  html?: string;

  @ApiProperty({
    description: 'Email template name',
    example: 'order-confirmation',
    required: false,
  })
  @IsString()
  @IsOptional()
  template?: string;

  @ApiProperty({
    description: 'Global variables applied to all emails',
    example: { companyName: 'ABC Inspections', supportEmail: '<EMAIL>' },
    required: false,
  })
  @IsObject()
  @IsOptional()
  globalVariables?: Record<string, any>;

  @ApiProperty({
    description: 'Email priority',
    enum: ['low', 'normal', 'high'],
    default: 'normal',
    required: false,
  })
  @IsEnum(['low', 'normal', 'high'])
  @IsOptional()
  priority?: 'low' | 'normal' | 'high' = 'normal';
}
