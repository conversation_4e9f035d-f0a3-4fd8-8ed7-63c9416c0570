import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as crypto from 'crypto';

import { Setting, SettingType, SettingCategory } from './entities/setting.entity';
import { CreateSettingDto } from './dto/create-setting.dto';
import { UpdateSettingDto } from './dto/update-setting.dto';
import { SettingQueryDto } from './dto/setting-query.dto';

@Injectable()
export class SettingsService {
  private readonly encryptionKey = process.env.SETTINGS_ENCRYPTION_KEY || 'default-key-change-in-production';

  constructor(
    @InjectRepository(Setting)
    private readonly settingRepository: Repository<Setting>,
  ) {}

  async create(createSettingDto: CreateSettingDto) {
    // Check if setting with key already exists
    const existingSetting = await this.settingRepository.findOne({
      where: { key: createSettingDto.key },
    });

    if (existingSetting) {
      throw new BadRequestException('Setting with this key already exists');
    }

    // Encrypt value if it's a secret setting
    let value = createSettingDto.value;
    if (createSettingDto.isSecret && value) {
      value = this.encryptValue(value);
    }

    const setting = this.settingRepository.create({
      ...createSettingDto,
      value,
    });

    const savedSetting = await this.settingRepository.save(setting);

    return {
      setting: this.formatSetting(savedSetting),
      message: 'Setting created successfully',
    };
  }

  async findAll(query: SettingQueryDto) {
    const { category, group, isVisible, search } = query;

    const queryBuilder = this.settingRepository.createQueryBuilder('setting');

    // Apply filters
    if (category) {
      queryBuilder.andWhere('setting.category = :category', { category });
    }

    if (group) {
      queryBuilder.andWhere('setting.group = :group', { group });
    }

    if (isVisible !== undefined) {
      queryBuilder.andWhere('setting.isVisible = :isVisible', { isVisible });
    }

    if (search) {
      queryBuilder.andWhere(
        '(setting.name ILIKE :search OR setting.key ILIKE :search OR setting.description ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Apply sorting
    queryBuilder.orderBy('setting.category', 'ASC');
    queryBuilder.addOrderBy('setting.sortOrder', 'ASC');
    queryBuilder.addOrderBy('setting.name', 'ASC');

    const settings = await queryBuilder.getMany();

    return {
      settings: settings.map(setting => this.formatSetting(setting)),
    };
  }

  async findOne(id: number) {
    const setting = await this.settingRepository.findOne({ where: { id } });

    if (!setting) {
      throw new NotFoundException('Setting not found');
    }

    return this.formatSetting(setting);
  }

  async getByKey(key: string) {
    const setting = await this.settingRepository.findOne({ where: { key } });

    if (!setting) {
      throw new NotFoundException('Setting not found');
    }

    return this.formatSetting(setting);
  }

  async getValue(key: string, defaultValue?: any) {
    const setting = await this.settingRepository.findOne({ where: { key } });

    if (!setting) {
      return defaultValue;
    }

    return this.parseValue(setting);
  }

  async getByCategory(category: string) {
    const settings = await this.settingRepository.find({
      where: { category: category as SettingCategory },
      order: { sortOrder: 'ASC', name: 'ASC' },
    });

    return {
      category,
      settings: settings.map(setting => this.formatSetting(setting)),
    };
  }

  async update(id: number, updateSettingDto: UpdateSettingDto) {
    const setting = await this.settingRepository.findOne({ where: { id } });

    if (!setting) {
      throw new NotFoundException('Setting not found');
    }

    // Encrypt value if it's a secret setting
    let value = updateSettingDto.value;
    if (setting.isSecret && value) {
      value = this.encryptValue(value);
    }

    await this.settingRepository.update(id, {
      ...updateSettingDto,
      value,
    });

    const updatedSetting = await this.findOne(id);
    return {
      setting: updatedSetting,
      message: 'Setting updated successfully',
    };
  }

  async updateValue(key: string, value: any) {
    const setting = await this.settingRepository.findOne({ where: { key } });

    if (!setting) {
      throw new NotFoundException('Setting not found');
    }

    if (!setting.isEditable) {
      throw new BadRequestException('This setting is not editable');
    }

    // Validate the value
    const validation = this.validateSetting(key, value);
    if (!validation.isValid) {
      throw new BadRequestException(`Invalid value: ${validation.errors.join(', ')}`);
    }

    // Encrypt value if it's a secret setting
    let encryptedValue = value;
    if (setting.isSecret && value) {
      encryptedValue = this.encryptValue(String(value));
    }

    await this.settingRepository.update({ key }, { value: encryptedValue });

    return {
      message: 'Setting value updated successfully',
      value: this.parseValue({ ...setting, value: encryptedValue }),
    };
  }

  async bulkUpdate(settings: { key: string; value: any }[]) {
    const results = [];

    for (const { key, value } of settings) {
      try {
        const result = await this.updateValue(key, value);
        results.push({ key, success: true, ...result });
      } catch (error) {
        results.push({ key, success: false, error: error.message });
      }
    }

    return {
      results,
      message: 'Bulk update completed',
    };
  }

  async resetToDefault(key: string) {
    const setting = await this.settingRepository.findOne({ where: { key } });

    if (!setting) {
      throw new NotFoundException('Setting not found');
    }

    await this.settingRepository.update({ key }, { value: setting.defaultValue });

    return {
      message: 'Setting reset to default value',
      value: this.parseValue({ ...setting, value: setting.defaultValue }),
    };
  }

  async remove(id: number) {
    const setting = await this.settingRepository.findOne({ where: { id } });

    if (!setting) {
      throw new NotFoundException('Setting not found');
    }

    await this.settingRepository.remove(setting);

    return {
      message: 'Setting deleted successfully',
    };
  }

  async getPublicSettings() {
    const settings = await this.settingRepository.find({
      where: { isVisible: true, isSecret: false },
      order: { category: 'ASC', sortOrder: 'ASC' },
    });

    const groupedSettings = settings.reduce((acc, setting) => {
      const category = setting.category;
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push({
        key: setting.key,
        name: setting.name,
        value: this.parseValue(setting),
        type: setting.type,
      });
      return acc;
    }, {});

    return groupedSettings;
  }

  async getCategories() {
    return Object.values(SettingCategory).map((category) => ({
      value: category,
      label: category.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
    }));
  }

  async createBackup() {
    const settings = await this.settingRepository.find();
    
    const backup = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      settings: settings.map(setting => ({
        key: setting.key,
        value: setting.isSecret ? '[ENCRYPTED]' : setting.value,
        type: setting.type,
        category: setting.category,
      })),
    };

    return {
      backup,
      message: 'Settings backup created successfully',
    };
  }

  async restoreBackup(backupData: any) {
    // This is a simplified restore - in production, you'd want more validation
    const { settings } = backupData;
    const results = [];

    for (const { key, value } of settings) {
      if (value !== '[ENCRYPTED]') {
        try {
          await this.updateValue(key, value);
          results.push({ key, success: true });
        } catch (error) {
          results.push({ key, success: false, error: error.message });
        }
      }
    }

    return {
      results,
      message: 'Settings restore completed',
    };
  }

  validateSetting(key: string, value: any) {
    // This would contain validation logic based on setting type and options
    const errors: string[] = [];

    // Basic validation - extend as needed
    if (value === null || value === undefined) {
      return { isValid: true, errors: [] };
    }

    // Add more validation rules here

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private formatSetting(setting: Setting) {
    const formatted = {
      ...setting,
      value: this.parseValue(setting),
    };

    // Hide encrypted values in responses
    if (setting.isSecret) {
      formatted.value = '[HIDDEN]';
    }

    return formatted;
  }

  private parseValue(setting: Setting) {
    if (!setting.value) {
      return setting.defaultValue ? this.parseValueByType(setting.defaultValue, setting.type) : null;
    }

    // Decrypt if secret
    let value = setting.value;
    if (setting.isSecret) {
      try {
        value = this.decryptValue(value);
      } catch (error) {
        return null;
      }
    }

    return this.parseValueByType(value, setting.type);
  }

  private parseValueByType(value: string, type: SettingType) {
    switch (type) {
      case SettingType.BOOLEAN:
        return value === 'true';
      case SettingType.NUMBER:
        return parseFloat(value);
      case SettingType.JSON:
        try {
          return JSON.parse(value);
        } catch {
          return null;
        }
      default:
        return value;
    }
  }

  private encryptValue(value: string): string {
    const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
    let encrypted = cipher.update(value, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }

  private decryptValue(encryptedValue: string): string {
    const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
    let decrypted = decipher.update(encryptedValue, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
}
