import { JwtModuleOptions } from '@nestjs/jwt';

export const jwtConfig = (): JwtModuleOptions => ({
  secret: process.env.JWT_SECRET || 'your-secret-key-at-least-32-chars-long',
  signOptions: {
    expiresIn: process.env.JWT_EXPIRES_IN || '15m',
  },
});

export const refreshTokenConfig = (): JwtModuleOptions => ({
  secret: process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key',
  signOptions: {
    expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  },
});
