import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { OrderInspector, InspectorRole } from '../entities/order-inspector.entity';
import { Order } from '../entities/order.entity';
import { User } from '@/modules/users/entities/user.entity';

@Injectable()
export class OrderInspectorService {
  constructor(
    @InjectRepository(OrderInspector)
    private readonly orderInspectorRepository: Repository<OrderInspector>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Get inspectors for an order
   */
  async getOrderInspectors(orderId: number): Promise<User[]> {
    const orderInspectors = await this.orderInspectorRepository.find({
      where: { orderId, isActive: true },
      relations: ['inspector'],
      order: { role: 'ASC', createdAt: 'ASC' },
    });

    return orderInspectors.map(oi => oi.inspector);
  }

  /**
   * Get orders for a specific inspector
   */
  async getInspectorOrders(inspectorId: number): Promise<Order[]> {
    const orderInspectors = await this.orderInspectorRepository.find({
      where: { inspectorId, isActive: true },
      relations: ['order'],
    });

    return orderInspectors.map(oi => oi.order);
  }

  /**
   * Add inspector to order
   */
  async addInspectorToOrder(
    orderId: number,
    inspectorId: number,
    role: InspectorRole = InspectorRole.PRIMARY,
    assignedBy?: number,
    specialInstructions?: string,
  ): Promise<OrderInspector> {
    // Check if order exists
    const order = await this.orderRepository.findOne({ where: { id: orderId } });
    if (!order) {
      throw new NotFoundException('Order not found');
    }

    // Check if inspector exists and is active
    const inspector = await this.userRepository.findOne({
      where: { id: inspectorId, isActive: true },
    });
    if (!inspector) {
      throw new NotFoundException('Inspector not found or inactive');
    }

    // Check if inspector is already assigned to this order
    const existingAssignment = await this.orderInspectorRepository.findOne({
      where: { orderId, inspectorId },
    });

    if (existingAssignment) {
      if (existingAssignment.isActive) {
        throw new BadRequestException('Inspector is already assigned to this order');
      } else {
        // Reactivate existing assignment
        existingAssignment.isActive = true;
        existingAssignment.role = role;
        existingAssignment.assignedAt = new Date();
        existingAssignment.assignedBy = assignedBy;
        existingAssignment.specialInstructions = specialInstructions;
        return await this.orderInspectorRepository.save(existingAssignment);
      }
    }

    // Create new assignment
    const orderInspector = this.orderInspectorRepository.create({
      orderId,
      inspectorId,
      role,
      assignedAt: new Date(),
      assignedBy,
      specialInstructions,
      isActive: true,
    });

    return await this.orderInspectorRepository.save(orderInspector);
  }

  /**
   * Remove inspector from order
   */
  async removeInspectorFromOrder(orderId: number, inspectorId: number): Promise<void> {
    const orderInspector = await this.orderInspectorRepository.findOne({
      where: { orderId, inspectorId, isActive: true },
    });

    if (!orderInspector) {
      throw new NotFoundException('Inspector assignment not found');
    }

    // Soft delete by setting isActive to false
    orderInspector.isActive = false;
    await this.orderInspectorRepository.save(orderInspector);
  }

  /**
   * Update inspector assignment
   */
  async updateInspectorAssignment(
    orderId: number,
    inspectorId: number,
    updates: Partial<{
      role: InspectorRole;
      specialInstructions: string;
    }>,
  ): Promise<OrderInspector> {
    const orderInspector = await this.orderInspectorRepository.findOne({
      where: { orderId, inspectorId, isActive: true },
    });

    if (!orderInspector) {
      throw new NotFoundException('Inspector assignment not found');
    }

    Object.assign(orderInspector, updates);
    return await this.orderInspectorRepository.save(orderInspector);
  }

  /**
   * Replace all inspectors for an order
   */
  async replaceOrderInspectors(
    orderId: number,
    inspectorIds: number[],
    assignedBy?: number,
  ): Promise<OrderInspector[]> {
    // Validate all inspectors exist and are active
    if (inspectorIds.length > 0) {
      const inspectors = await this.userRepository.find({
        where: { id: In(inspectorIds), isActive: true },
      });
      if (inspectors.length !== inspectorIds.length) {
        throw new NotFoundException('One or more inspectors not found or inactive');
      }
    }

    // Deactivate all current assignments
    await this.orderInspectorRepository.update(
      { orderId, isActive: true },
      { isActive: false },
    );

    // Add new assignments
    const newAssignments: OrderInspector[] = [];
    for (let i = 0; i < inspectorIds.length; i++) {
      const role = i === 0 ? InspectorRole.PRIMARY : InspectorRole.SECONDARY;
      const assignment = await this.addInspectorToOrder(
        orderId,
        inspectorIds[i],
        role,
        assignedBy,
      );
      newAssignments.push(assignment);
    }

    return newAssignments;
  }

  /**
   * Check if inspector is assigned to order
   */
  async isInspectorAssignedToOrder(orderId: number, inspectorId: number): Promise<boolean> {
    const assignment = await this.orderInspectorRepository.findOne({
      where: { orderId, inspectorId, isActive: true },
    });
    return !!assignment;
  }

  /**
   * Get inspector IDs for an order (for backward compatibility)
   */
  async getOrderInspectorIds(orderId: number): Promise<number[]> {
    const orderInspectors = await this.orderInspectorRepository.find({
      where: { orderId, isActive: true },
      order: { role: 'ASC', createdAt: 'ASC' },
    });

    return orderInspectors.map(oi => oi.inspectorId);
  }

  /**
   * Get primary inspector for an order
   */
  async getPrimaryInspector(orderId: number): Promise<User | null> {
    const orderInspector = await this.orderInspectorRepository.findOne({
      where: { orderId, role: InspectorRole.PRIMARY, isActive: true },
      relations: ['inspector'],
    });

    return orderInspector?.inspector || null;
  }
}
