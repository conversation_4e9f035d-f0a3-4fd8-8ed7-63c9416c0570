import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsEnum,
  IsObject,
  IsNotEmpty,
  Matches,
} from 'class-validator';
import { JobType } from '../entities/cronjob.entity';

export class CreateCronjobDto {
  @ApiProperty({
    description: 'Job name',
    example: 'Daily Email Reminders',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Job description',
    example: 'Send email reminders for upcoming inspections',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Cron schedule expression',
    example: '0 9 * * *',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^(\*|([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])|\*\/([0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])) (\*|([0-9]|1[0-9]|2[0-3])|\*\/([0-9]|1[0-9]|2[0-3])) (\*|([1-9]|1[0-9]|2[0-9]|3[0-1])|\*\/([1-9]|1[0-9]|2[0-9]|3[0-1])) (\*|([1-9]|1[0-2])|\*\/([1-9]|1[0-2])) (\*|([0-6])|\*\/([0-6]))$/, {
    message: 'Invalid cron expression',
  })
  schedule: string;

  @ApiProperty({
    description: 'Job type',
    enum: JobType,
    example: JobType.EMAIL_REMINDER,
  })
  @IsEnum(JobType)
  jobType: JobType;

  @ApiProperty({
    description: 'Job configuration parameters',
    example: {
      reminderHours: 24,
      emailTemplate: 'inspection-reminder',
    },
    required: false,
  })
  @IsObject()
  @IsOptional()
  configuration?: Record<string, any>;

  @ApiProperty({
    description: 'Is job active',
    default: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean = true;

  @ApiProperty({
    description: 'Job timeout in seconds',
    example: 300,
    required: false,
  })
  @IsOptional()
  timeout?: number;

  @ApiProperty({
    description: 'Maximum retry attempts',
    example: 3,
    required: false,
  })
  @IsOptional()
  maxRetries?: number;
}
