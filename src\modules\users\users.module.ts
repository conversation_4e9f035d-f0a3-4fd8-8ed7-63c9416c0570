import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { User } from './entities/user.entity';
import { Order } from '../orders/entities/order.entity';
import { OrderClient } from '../orders/entities/order-client.entity';
import { OrderInspector } from '../orders/entities/order-inspector.entity';

@Module({
  imports: [TypeOrmModule.forFeature([User, Order, OrderClient, OrderInspector])],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
