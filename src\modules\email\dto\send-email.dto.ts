import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsArray,
  IsObject,
  IsEnum,
  IsNotEmpty,
  IsEmail,
} from 'class-validator';

export class SendEmailDto {
  @ApiProperty({
    description: 'Recipient email address(es)',
    example: '<EMAIL>',
    oneOf: [
      { type: 'string' },
      { type: 'array', items: { type: 'string' } },
    ],
  })
  @IsNotEmpty()
  to: string | string[];

  @ApiProperty({
    description: 'Email subject',
    example: 'Order Confirmation - ORD-2024-001',
  })
  @IsString()
  @IsNotEmpty()
  subject: string;

  @ApiProperty({
    description: 'Plain text email content',
    example: 'Your order has been confirmed.',
    required: false,
  })
  @IsString()
  @IsOptional()
  text?: string;

  @ApiProperty({
    description: 'HTML email content',
    example: '<h1>Order Confirmed</h1><p>Your order has been confirmed.</p>',
    required: false,
  })
  @IsString()
  @IsOptional()
  html?: string;

  @ApiProperty({
    description: 'Email template name',
    example: 'order-confirmation',
    required: false,
  })
  @IsString()
  @IsOptional()
  template?: string;

  @ApiProperty({
    description: 'Template variables',
    example: {
      clientName: 'John Doe',
      orderNumber: 'ORD-2024-001',
      propertyAddress: '123 Main St',
    },
    required: false,
  })
  @IsObject()
  @IsOptional()
  variables?: Record<string, any>;

  @ApiProperty({
    description: 'Email attachments',
    type: [Object],
    required: false,
  })
  @IsArray()
  @IsOptional()
  attachments?: any[];

  @ApiProperty({
    description: 'Email priority',
    enum: ['low', 'normal', 'high'],
    default: 'normal',
    required: false,
  })
  @IsEnum(['low', 'normal', 'high'])
  @IsOptional()
  priority?: 'low' | 'normal' | 'high' = 'normal';
}
