import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { Order } from '../../orders/entities/order.entity';
import { PropertyTag } from './property-tag.entity';

export enum PropertyType {
  SINGLE_FAMILY = 'single_family',
  CONDO = 'condo',
  TOWNHOUSE = 'townhouse',
  MULTI_FAMILY = 'multi_family',
  COMMERCIAL = 'commercial',
  LAND = 'land',
}

export enum PropertyStatus {
  ACTIVE = 'active',
  SOLD = 'sold',
  PENDING = 'pending',
  WITHDRAWN = 'withdrawn',
}

@Entity('properties')
export class Property {
  @PrimaryGeneratedColumn()
  id: number;

  // Address Information
  @Column({ length: 500 })
  addressLine1: string;

  @Column({ nullable: true, length: 500 })
  addressLine2: string;

  @Column({ length: 255 })
  city: string;

  @Column({ length: 50 })
  state: string;

  @Column({ length: 10 })
  zipCode: string;

  @Column({ nullable: true, length: 100 })
  country: string;

  // Property Details
  @Column({
    type: 'enum',
    enum: PropertyType,
  })
  propertyType: PropertyType;

  @Column({
    type: 'enum',
    enum: PropertyStatus,
    default: PropertyStatus.ACTIVE,
  })
  status: PropertyStatus;

  @Column({ nullable: true })
  yearBuilt: number;

  @Column({ nullable: true, type: 'decimal', precision: 10, scale: 2 })
  squareFootage: number;

  @Column({ nullable: true, type: 'decimal', precision: 3, scale: 1 })
  lotSize: number;

  @Column({ nullable: true })
  bedrooms: number;

  @Column({ nullable: true, type: 'decimal', precision: 3, scale: 1 })
  bathrooms: number;

  @Column({ nullable: true })
  floors: number;

  @Column({ nullable: true, length: 100 })
  foundationType: string;

  // Property Features
  @Column({ default: false })
  hasGarage: boolean;

  @Column({ default: false })
  hasPool: boolean;

  @Column({ default: false })
  hasFireplace: boolean;

  @Column({ default: false })
  hasBasement: boolean;

  @Column({ default: false })
  hasAttic: boolean;

  @Column({ default: false })
  hasDeck: boolean;

  @Column({ default: false })
  hasPatio: boolean;

  // Utilities & Systems
  @Column({ default: false })
  hasElectricity: boolean;

  @Column({ default: false })
  hasWater: boolean;

  @Column({ default: false })
  hasGas: boolean;

  @Column({ default: false })
  hasSewer: boolean;

  @Column({ default: false })
  hasInternet: boolean;

  @Column({ nullable: true, length: 100 })
  heatingType: string;

  @Column({ nullable: true, length: 100 })
  coolingType: string;

  // Access Information
  @Column({ nullable: true, length: 100 })
  gateCode: string;

  @Column({ nullable: true, length: 100 })
  lockboxCode: string;

  @Column({ nullable: true, length: 100 })
  alarmCode: string;

  @Column({ nullable: true, length: 100 })
  mlsNumber: string;

  // Additional Information
  @Column({ nullable: true, type: 'text' })
  description: string;

  @Column({ nullable: true, type: 'text' })
  notes: string;

  @Column({ type: 'text', array: true, nullable: true })
  images: string[];

  @Column({ type: 'jsonb', nullable: true })
  customFields: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToMany(() => Order, (order) => order.property)
  orders: Order[];

  @ManyToMany(() => PropertyTag, (tag) => tag.properties)
  @JoinTable({
    name: 'property_property_tags',
    joinColumn: { name: 'propertyId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'tagId', referencedColumnName: 'id' },
  })
  tags: PropertyTag[];
}
