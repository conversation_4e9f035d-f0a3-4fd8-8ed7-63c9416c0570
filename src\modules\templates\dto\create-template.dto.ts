import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsEnum,
  IsArray,
  IsObject,
  IsNotEmpty,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TemplateType, TemplateCategory } from '../entities/template.entity';

class TemplateVariableDto {
  @ApiProperty({ description: 'Variable name', example: 'clientName' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Variable description', example: 'Client full name' })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ description: 'Is variable required', default: false })
  @IsBoolean()
  required: boolean;

  @ApiProperty({ description: 'Default value', required: false })
  @IsString()
  @IsOptional()
  defaultValue?: string;
}

class TemplateMetadataDto {
  @ApiProperty({ description: 'Template description', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Template tags', type: [String], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @ApiProperty({ description: 'Template version', required: false })
  @IsString()
  @IsOptional()
  version?: string;

  @ApiProperty({ description: 'Template author', required: false })
  @IsString()
  @IsOptional()
  author?: string;
}

class TemplateStylingDto {
  @ApiProperty({ description: 'Background color', required: false })
  @IsString()
  @IsOptional()
  backgroundColor?: string;

  @ApiProperty({ description: 'Text color', required: false })
  @IsString()
  @IsOptional()
  textColor?: string;

  @ApiProperty({ description: 'Font family', required: false })
  @IsString()
  @IsOptional()
  fontFamily?: string;

  @ApiProperty({ description: 'Font size', required: false })
  @IsString()
  @IsOptional()
  fontSize?: string;

  @ApiProperty({ description: 'Custom CSS', required: false })
  @IsString()
  @IsOptional()
  customCss?: string;
}

export class CreateTemplateDto {
  @ApiProperty({
    description: 'Template name',
    example: 'Order Confirmation Email',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Template subject',
    example: 'Your inspection order has been confirmed - {{orderNumber}}',
  })
  @IsString()
  @IsNotEmpty()
  subject: string;

  @ApiProperty({
    description: 'Template type',
    enum: TemplateType,
    example: TemplateType.EMAIL,
  })
  @IsEnum(TemplateType)
  type: TemplateType;

  @ApiProperty({
    description: 'Template category',
    enum: TemplateCategory,
    example: TemplateCategory.ORDER_CONFIRMATION,
  })
  @IsEnum(TemplateCategory)
  category: TemplateCategory;

  @ApiProperty({
    description: 'Template content (text version)',
    example: 'Dear {{clientName}}, your order {{orderNumber}} has been confirmed.',
  })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({
    description: 'Template HTML content',
    example: '<h1>Order Confirmed</h1><p>Dear {{clientName}}, your order {{orderNumber}} has been confirmed.</p>',
    required: false,
  })
  @IsString()
  @IsOptional()
  htmlContent?: string;

  @ApiProperty({
    description: 'Template variables',
    type: [TemplateVariableDto],
    required: false,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TemplateVariableDto)
  @IsOptional()
  variables?: TemplateVariableDto[];

  @ApiProperty({
    description: 'Template metadata',
    type: TemplateMetadataDto,
    required: false,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => TemplateMetadataDto)
  @IsOptional()
  metadata?: TemplateMetadataDto;

  @ApiProperty({
    description: 'Template active status',
    default: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean = true;

  @ApiProperty({
    description: 'Is default template for category',
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isDefault?: boolean = false;

  @ApiProperty({
    description: 'Template language',
    example: 'en',
    required: false,
  })
  @IsString()
  @IsOptional()
  language?: string;

  @ApiProperty({
    description: 'Preview text for email clients',
    required: false,
  })
  @IsString()
  @IsOptional()
  previewText?: string;

  @ApiProperty({
    description: 'Template styling',
    type: TemplateStylingDto,
    required: false,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => TemplateStylingDto)
  @IsOptional()
  styling?: TemplateStylingDto;
}
