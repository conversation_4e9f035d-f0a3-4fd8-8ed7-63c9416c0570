import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';

import { TemplatesService } from './templates.service';
import { CreateTemplateDto } from './dto/create-template.dto';
import { UpdateTemplateDto } from './dto/update-template.dto';
import { TemplateQueryDto } from './dto/template-query.dto';
import { RenderTemplateDto } from './dto/render-template.dto';
import { Auth } from '../../common/decorators/auth.decorator';

@ApiTags('Templates')
@Controller('templates')
export class TemplatesController {
  constructor(private readonly templatesService: TemplatesService) {}

  @Post()
  @Auth('admin')
  @ApiOperation({ summary: 'Create a new template' })
  @ApiResponse({ status: 201, description: 'Template successfully created' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async create(@Body() createTemplateDto: CreateTemplateDto) {
    return this.templatesService.create(createTemplateDto);
  }

  @Get()
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Get all templates with filtering' })
  @ApiResponse({ status: 200, description: 'Templates retrieved successfully' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'type', required: false, type: String })
  @ApiQuery({ name: 'category', required: false, type: String })
  @ApiQuery({ name: 'isActive', required: false, type: Boolean })
  @ApiQuery({ name: 'search', required: false, type: String })
  async findAll(@Query() query: TemplateQueryDto) {
    return this.templatesService.findAll(query);
  }

  @Get('categories')
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Get all template categories' })
  @ApiResponse({ status: 200, description: 'Categories retrieved successfully' })
  async getCategories() {
    return this.templatesService.getCategories();
  }

  @Get('variables')
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Get available template variables' })
  @ApiResponse({ status: 200, description: 'Variables retrieved successfully' })
  async getAvailableVariables() {
    return this.templatesService.getAvailableVariables();
  }

  @Get(':id')
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Get template by ID' })
  @ApiResponse({ status: 200, description: 'Template retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Template not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.templatesService.findOne(id);
  }

  @Post(':id/render')
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Render template with variables' })
  @ApiResponse({ status: 200, description: 'Template rendered successfully' })
  @ApiResponse({ status: 404, description: 'Template not found' })
  async render(
    @Param('id', ParseIntPipe) id: number,
    @Body() renderData: RenderTemplateDto,
  ) {
    return this.templatesService.renderTemplate(id, renderData.variables);
  }

  @Post(':id/preview')
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Preview template with sample data' })
  @ApiResponse({ status: 200, description: 'Template preview generated' })
  async preview(@Param('id', ParseIntPipe) id: number) {
    return this.templatesService.previewTemplate(id);
  }

  @Post(':id/duplicate')
  @Auth('admin')
  @ApiOperation({ summary: 'Duplicate template' })
  @ApiResponse({ status: 201, description: 'Template duplicated successfully' })
  async duplicate(@Param('id', ParseIntPipe) id: number) {
    return this.templatesService.duplicateTemplate(id);
  }

  @Patch(':id')
  @Auth('admin')
  @ApiOperation({ summary: 'Update template' })
  @ApiResponse({ status: 200, description: 'Template updated successfully' })
  @ApiResponse({ status: 404, description: 'Template not found' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateTemplateDto: UpdateTemplateDto,
  ) {
    return this.templatesService.update(id, updateTemplateDto);
  }

  @Patch(':id/activate')
  @Auth('admin')
  @ApiOperation({ summary: 'Activate template' })
  @ApiResponse({ status: 200, description: 'Template activated successfully' })
  async activate(@Param('id', ParseIntPipe) id: number) {
    return this.templatesService.updateStatus(id, true);
  }

  @Patch(':id/deactivate')
  @Auth('admin')
  @ApiOperation({ summary: 'Deactivate template' })
  @ApiResponse({ status: 200, description: 'Template deactivated successfully' })
  async deactivate(@Param('id', ParseIntPipe) id: number) {
    return this.templatesService.updateStatus(id, false);
  }

  @Delete(':id')
  @Auth('admin')
  @ApiOperation({ summary: 'Delete template' })
  @ApiResponse({ status: 200, description: 'Template deleted successfully' })
  @ApiResponse({ status: 404, description: 'Template not found' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.templatesService.remove(id);
  }

  @Get('category/:category')
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Get templates by category' })
  @ApiResponse({ status: 200, description: 'Templates retrieved successfully' })
  async findByCategory(@Param('category') category: string) {
    return this.templatesService.findByCategory(category);
  }

  @Post('validate')
  @Auth('admin')
  @ApiOperation({ summary: 'Validate template syntax' })
  @ApiResponse({ status: 200, description: 'Template validation result' })
  async validateTemplate(@Body() templateData: { content: string; htmlContent?: string }) {
    return this.templatesService.validateTemplate(templateData.content, templateData.htmlContent);
  }
}
