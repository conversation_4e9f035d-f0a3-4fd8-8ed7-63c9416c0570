# 🔄 Migration Guide: Next.js API → NestJS Modular

## 📊 So sánh Architecture

### Before (Next.js API Routes)
```
order-mgmt-api/
├── app/api/
│   ├── auth/
│   ├── orders/
│   ├── inspectors/
│   └── schedules/
├── lib/
│   ├── db.ts
│   ├── jwt.ts
│   └── validation.ts
└── middleware/
```

### After (NestJS Modular)
```
nestjs-order-mgmt-api/
├── src/
│   ├── modules/
│   │   ├── auth/
│   │   ├── orders/
│   │   ├── inspectors/
│   │   └── schedules/
│   ├── common/
│   └── config/
```

## 🔄 Endpoint Migration Map

| Next.js Route | NestJS Module | Controller | Method |
|---------------|---------------|------------|---------|
| `/api/auth/login` | AuthModule | AuthController | `POST /auth/login` |
| `/api/auth/register` | AuthModule | AuthController | `POST /auth/register` |
| `/api/auth/verify` | AuthModule | AuthController | `POST /auth/verify` |
| `/api/orders` | OrdersModule | OrdersController | `GET,POST /orders` |
| `/api/orders/[id]` | OrdersModule | OrdersController | `GET,PATCH,DELETE /orders/:id` |
| `/api/inspectors` | InspectorsModule | InspectorsController | `GET,POST /inspectors` |
| `/api/inspectors/[id]` | InspectorsModule | InspectorsController | `GET,PATCH,DELETE /inspectors/:id` |
| `/api/schedules` | SchedulesModule | SchedulesController | `GET,POST /schedules` |
| `/api/schedules/[id]` | SchedulesModule | SchedulesController | `GET,PATCH,DELETE /schedules/:id` |
| `/api/email/send` | EmailModule | EmailController | `POST /email/send` |
| `/api/cronjobs` | CronjobsModule | CronjobsController | `GET,POST /cronjobs` |

## 🛠️ Technical Improvements

### 1. Database Layer

**Before (Raw SQL)**:
```typescript
// lib/db.ts
const result = await sql`
  SELECT * FROM inspection_orders 
  WHERE status = ${status}
`;
```

**After (TypeORM)**:
```typescript
// orders.service.ts
const orders = await this.orderRepository.find({
  where: { status },
  relations: ['client', 'schedules'],
});
```

### 2. Validation

**Before (Zod)**:
```typescript
// lib/validation.ts
const orderSchema = z.object({
  clientName: z.string().min(1),
  clientEmail: z.string().email(),
});
```

**After (Class Validator)**:
```typescript
// dto/create-order.dto.ts
export class CreateOrderDto {
  @IsString()
  @IsNotEmpty()
  clientName: string;

  @IsEmail()
  clientEmail: string;
}
```

### 3. Authentication

**Before (Custom Middleware)**:
```typescript
// middleware/auth.ts
export function authMiddleware(req, res, next) {
  const token = req.headers.authorization;
  // Manual JWT verification
}
```

**After (Passport Strategy)**:
```typescript
// auth/strategies/jwt.strategy.ts
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  async validate(payload: any) {
    return { userId: payload.sub, email: payload.email };
  }
}
```

### 4. Error Handling

**Before (Manual)**:
```typescript
// api/orders/route.ts
try {
  // logic
} catch (error) {
  return NextResponse.json({ error: error.message }, { status: 500 });
}
```

**After (Exception Filters)**:
```typescript
// common/filters/http-exception.filter.ts
@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    // Centralized error handling
  }
}
```

## 🔐 Security Enhancements

### 1. Role-based Access Control

**Before**:
```typescript
// Manual role checking in each route
if (user.role !== 'admin') {
  return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
}
```

**After**:
```typescript
// Using decorators
@Auth('admin', 'inspector')
@Get()
async findAll() {
  // Automatic role checking
}
```

### 2. Input Validation

**Before**:
```typescript
// Manual validation
const { error } = orderSchema.safeParse(body);
if (error) {
  return NextResponse.json({ error: error.message }, { status: 400 });
}
```

**After**:
```typescript
// Automatic validation with DTOs
@Post()
async create(@Body() createOrderDto: CreateOrderDto) {
  // Validation happens automatically
}
```

## 📚 API Documentation

### Before (Manual Swagger)
```typescript
// lib/swagger.ts
const spec = {
  paths: {
    '/api/orders': {
      get: {
        summary: 'Get orders',
        // Manual documentation
      }
    }
  }
};
```

### After (Auto-generated)
```typescript
// orders.controller.ts
@ApiTags('Orders')
@ApiOperation({ summary: 'Get orders' })
@ApiResponse({ status: 200, description: 'Orders retrieved' })
@Get()
async findAll() {
  // Documentation generated automatically
}
```

## 🔄 Background Jobs

### Before (Custom Worker)
```typescript
// workers/cronjob-worker.ts
class CronjobWorker {
  async processJob(job) {
    // Custom job processing
  }
}
```

### After (Bull Queue)
```typescript
// email/email.processor.ts
@Processor('email')
export class EmailProcessor {
  @Process('send')
  async handleSendEmail(job: Job) {
    // Queue-based processing with retry logic
  }
}
```

## 📧 Email System

### Before (Direct Nodemailer)
```typescript
// lib/email.ts
const transporter = nodemailer.createTransporter(config);
await transporter.sendMail(mailOptions);
```

### After (Queue-based)
```typescript
// email/email.service.ts
@Injectable()
export class EmailService {
  async sendEmail(emailData: SendEmailDto) {
    await this.emailQueue.add('send', emailData, {
      attempts: 3,
      backoff: 'exponential',
    });
  }
}
```

## 🚀 Performance Improvements

### 1. Database Connections
- **Before**: Manual connection management
- **After**: Connection pooling with TypeORM

### 2. Caching
- **Before**: No built-in caching
- **After**: Redis integration for caching and queues

### 3. Request Processing
- **Before**: Sequential processing
- **After**: Async/await with proper error boundaries

## 🧪 Testing

### Before (Limited)
```typescript
// Basic API testing
describe('Orders API', () => {
  it('should create order', async () => {
    // Manual test setup
  });
});
```

### After (Comprehensive)
```typescript
// orders.controller.spec.ts
describe('OrdersController', () => {
  let controller: OrdersController;
  let service: OrdersService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      controllers: [OrdersController],
      providers: [OrdersService],
    }).compile();
    // Dependency injection testing
  });
});
```

## 📈 Scalability Benefits

### 1. Modular Architecture
- **Separation of Concerns**: Each module handles specific functionality
- **Dependency Injection**: Loose coupling between components
- **Easy Testing**: Mock dependencies easily

### 2. Type Safety
- **End-to-end TypeScript**: From DTOs to entities
- **Compile-time Checks**: Catch errors before runtime
- **IntelliSense Support**: Better developer experience

### 3. Maintainability
- **Consistent Structure**: Standard NestJS patterns
- **Auto-generated Documentation**: Always up-to-date
- **Built-in Best Practices**: Security, validation, error handling

## 🔧 Migration Steps

### 1. Setup New Project
```bash
npm i -g @nestjs/cli
nest new nestjs-order-mgmt-api
cd nestjs-order-mgmt-api
```

### 2. Install Dependencies
```bash
npm install @nestjs/typeorm typeorm pg
npm install @nestjs/jwt @nestjs/passport passport passport-jwt
npm install @nestjs/swagger swagger-ui-express
npm install @nestjs/bull bull redis
```

### 3. Create Modules
```bash
nest g module auth
nest g module orders
nest g module inspectors
nest g module schedules
```

### 4. Migrate Data Models
- Convert Zod schemas to TypeORM entities
- Create DTOs for validation
- Set up relationships

### 5. Migrate Business Logic
- Move service logic to NestJS services
- Convert API routes to controllers
- Implement guards and decorators

### 6. Testing & Deployment
- Write unit and integration tests
- Set up CI/CD pipeline
- Deploy to production

## ✅ Migration Checklist

- [ ] Database schema migration
- [ ] Authentication system
- [ ] API endpoints
- [ ] Background jobs
- [ ] Email system
- [ ] Error handling
- [ ] API documentation
- [ ] Testing suite
- [ ] Deployment configuration
- [ ] Monitoring setup

## 🎯 Next Steps

1. **Complete remaining modules** (Properties, Templates, Settings)
2. **Add comprehensive tests**
3. **Set up monitoring and logging**
4. **Implement caching strategies**
5. **Add rate limiting**
6. **Set up CI/CD pipeline**