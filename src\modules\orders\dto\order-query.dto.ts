import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsEnum, IsArray, Min, Matches } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { OrderStatus } from '../entities/order.entity';

export class OrderQueryDto {
  @ApiProperty({ description: 'Page number', default: 1, required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({ description: 'Items per page', default: 10, required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({
    description: 'Filter by status',
    enum: OrderStatus,
    required: false
  })
  @IsOptional()
  @IsEnum(OrderStatus)
  status?: OrderStatus;

  @ApiProperty({
    description: 'Filter by client ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === '' || value === '0' || value === 0) return undefined;
    return Number(value);
  })
  clientId?: number;

  @ApiProperty({
    description: 'Filter by inspector ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === '' || value === '0' || value === 0) return undefined;
    return Number(value);
  })
  inspectorId?: number;

  @ApiProperty({
    description: 'Filter by property ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  @Transform(({ value }) => {
    if (value === '' || value === '0' || value === 0) return undefined;
    return Number(value);
  })
  propertyId?: number;

  @ApiProperty({
    description: 'Filter by start date',
    example: '2024-01-01',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, { message: 'Start date must be in YYYY-MM-DD format' })
  @Transform(({ value }) => {
    if (!value || value === '' || !/^\d{4}-\d{2}-\d{2}$/.test(value)) return undefined;
    return value;
  })
  startDate?: string;

  @ApiProperty({
    description: 'Filter by end date',
    example: '2024-12-31',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, { message: 'End date must be in YYYY-MM-DD format' })
  @Transform(({ value }) => {
    if (!value || value === '' || !/^\d{4}-\d{2}-\d{2}$/.test(value)) return undefined;
    return value;
  })
  endDate?: string;

  @ApiProperty({
    description: 'Search in order number, client name, or address',
    required: false
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ 
    description: 'Sort by field', 
    default: 'createdAt', 
    required: false 
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiProperty({ 
    description: 'Sort order', 
    enum: ['ASC', 'DESC'], 
    default: 'DESC', 
    required: false 
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}
