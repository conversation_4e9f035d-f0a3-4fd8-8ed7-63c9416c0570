import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  ForbiddenException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';

import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
import { Auth } from '../../common/decorators/auth.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { AssignInspectorsDto } from './dto/assign-inspectors.dto';

@ApiTags('Orders')
@Controller('orders')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post()
  @Auth('admin', 'client')
  @ApiOperation({ summary: 'Create a new order' })
  @ApiResponse({ status: 201, description: 'Order successfully created' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async create(
    @Body() createOrderDto: CreateOrderDto,
    @CurrentUser() user: any,
  ) {
    return this.ordersService.create(createOrderDto, user.userId);
  }

  @Get()
  @Auth('admin', 'inspector', 'client')
  @ApiOperation({ summary: 'Get all orders with filtering' })
  @ApiResponse({ status: 200, description: 'Orders retrieved successfully' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'search', required: false, type: String })
  async findAll(
    @Query() query: OrderQueryDto,
    @CurrentUser() user: any,
  ) {
    return this.ordersService.findAll(query, user);
  }

  @Get(':id')
  @Auth('admin', 'inspector', 'client')
  @ApiOperation({ summary: 'Get order by ID' })
  @ApiResponse({ status: 200, description: 'Order retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: any,
  ) {
    return this.ordersService.findOne(id, user);
  }

  @Patch(':id')
  @Auth('admin', 'client')
  @ApiOperation({ summary: 'Update order' })
  @ApiResponse({ status: 200, description: 'Order updated successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateOrderDto: UpdateOrderDto,
    @CurrentUser() user: any,
  ) {
    return this.ordersService.update(id, updateOrderDto, user);
  }

  @Delete(':id')
  @Auth('admin')
  @ApiOperation({ summary: 'Delete order' })
  @ApiResponse({ status: 200, description: 'Order deleted successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: any,
  ) {
    return this.ordersService.remove(id, user);
  }

  @Post(':id/assign-inspectors')
  @Auth('admin')
  @ApiOperation({ summary: 'Assign inspectors to order' })
  @ApiResponse({ status: 200, description: 'Inspectors assigned successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  async assignInspectors(
    @Param('id', ParseIntPipe) id: number,
    @Body() assignData: AssignInspectorsDto,
    @CurrentUser() user: any,
  ) {
    return this.ordersService.assignInspectors(id, assignData.inspectorIds, user);
  }

  @Post(':id/schedule')
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Schedule an order' })
  @ApiResponse({ status: 200, description: 'Order scheduled successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  async schedule(
    @Param('id', ParseIntPipe) id: number,
    @Body() scheduleData: any,
    @CurrentUser() user: any,
  ) {
    return this.ordersService.scheduleInspection(id, scheduleData, user);
  }

  @Post(':id/complete')
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Complete inspection' })
  @ApiResponse({ status: 200, description: 'Inspection completed successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  async complete(
    @Param('id', ParseIntPipe) id: number,
    @Body() completionData: any,
    @CurrentUser() user: any,
  ) {
    return this.ordersService.completeInspection(id, completionData, user);
  }

  @Post(':id/cancel')
  @Auth('admin', 'client')
  @ApiOperation({ summary: 'Cancel order' })
  @ApiResponse({ status: 200, description: 'Order cancelled successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  async cancel(
    @Param('id', ParseIntPipe) id: number,
    @Body() cancelData: { reason: string },
    @CurrentUser() user: any,
  ) {
    return this.ordersService.cancelOrder(id, cancelData.reason, user);
  }

  @Get('stats')
  @Auth('admin')
  @ApiOperation({ summary: 'Get order statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  async getStats() {
    return this.ordersService.getOrderStats();
  }

  @Get('client/:clientId')
  @Auth('admin', 'client')
  @ApiOperation({ summary: 'Get orders for specific client' })
  @ApiResponse({ status: 200, description: 'Client orders retrieved successfully' })
  async getClientOrders(
    @Param('clientId', ParseIntPipe) clientId: number,
    @Query() query: any,
    @CurrentUser() user: any,
  ) {
    // Check permissions
    if (user.role === 'client' && user.userId !== clientId) {
      throw new ForbiddenException('You can only view your own orders');
    }
    return this.ordersService.getClientOrders(clientId, query);
  }

  @Get('inspector/:inspectorId')
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Get orders for specific inspector' })
  @ApiResponse({ status: 200, description: 'Inspector orders retrieved successfully' })
  async getInspectorOrders(
    @Param('inspectorId', ParseIntPipe) inspectorId: number,
    @Query() query: any,
    @CurrentUser() user: any,
  ) {
    // Check permissions
    if (user.role === 'inspector' && user.userId !== inspectorId) {
      throw new ForbiddenException('You can only view your own orders');
    }
    return this.ordersService.getInspectorOrders(inspectorId, query);
  }

  @Post(':id/add-client')
  @Auth('admin')
  @ApiOperation({ summary: 'Add client to order' })
  @ApiResponse({ status: 200, description: 'Client added successfully' })
  @ApiResponse({ status: 404, description: 'Order or client not found' })
  async addClient(
    @Param('id', ParseIntPipe) id: number,
    @Body() clientData: { clientId: number },
    @CurrentUser() user: any,
  ) {
    return this.ordersService.addClientToOrder(id, clientData.clientId, user);
  }

  @Delete(':id/remove-client/:clientId')
  @Auth('admin')
  @ApiOperation({ summary: 'Remove client from order' })
  @ApiResponse({ status: 200, description: 'Client removed successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  async removeClient(
    @Param('id', ParseIntPipe) id: number,
    @Param('clientId', ParseIntPipe) clientId: number,
    @CurrentUser() user: any,
  ) {
    return this.ordersService.removeClientFromOrder(id, clientId, user);
  }

  @Patch(':id/clients')
  @Auth('admin')
  @ApiOperation({ summary: 'Update order clients' })
  @ApiResponse({ status: 200, description: 'Order clients updated successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  async updateClients(
    @Param('id', ParseIntPipe) id: number,
    @Body() clientData: { clientIds: number[] },
    @CurrentUser() user: any,
  ) {
    return this.ordersService.updateOrderClients(id, clientData.clientIds, user);
  }

  @Get('clients/multiple')
  @Auth('admin')
  @ApiOperation({ summary: 'Get orders for multiple clients' })
  @ApiResponse({ status: 200, description: 'Orders retrieved successfully' })
  async getOrdersForClients(
    @Query() query: { clientIds: string } & any,
  ) {
    const clientIds = query.clientIds.split(',').map(id => parseInt(id));
    return this.ordersService.getOrdersForClients(clientIds, query);
  }
  
  @Get('schedules')
  @Auth('admin', 'inspector', 'client')
  @ApiOperation({ summary: 'Get all orders with filtering' })
  @ApiResponse({ status: 200, description: 'Orders retrieved successfully' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'search', required: false, type: String })
  async schedules(
    @Query() query: OrderQueryDto,
    @CurrentUser() user: any,
  ) {
    return this.ordersService.schedules(query, user);
  }
}
